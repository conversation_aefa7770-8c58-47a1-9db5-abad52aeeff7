import React from 'react';
import { useParams } from 'react-router-dom';
import { Box, Typography } from '@mui/material';

const VideoEditorPage = () => {
  const { projectId } = useParams();
  
  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
        Video Editor
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Project ID: {projectId} - Video editor implementation in progress...
      </Typography>
    </Box>
  );
};

export default VideoEditorPage;
